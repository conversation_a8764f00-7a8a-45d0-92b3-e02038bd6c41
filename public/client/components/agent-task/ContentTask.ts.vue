<template>
  <div class="h-full relative">
    <!-- AskRaleonPopup Overlay -->
    <!-- AskRaleonPopup with highlight control events -->
    <AskRaleonPopup
      :isVisible="isAskPopupVisible"
      :position="askPopupPosition"
      :selectedText="selectedTextContext"
      @submitQuery="handleAskRaleonQuerySubmit"
      @closePopup="isAskPopupVisible = false"
      @restoreSelection="() => {}"
      @setHighlight="handleSetHighlight"
      @removeHighlight="handleRemoveHighlight"
      @preventClose="handlePreventPopupClose"
    />
    <!-- Main Flex Container: Campaign Brief (left) | BriefChat (right) -->
    <div class="flex h-[calc(100%-10px)] relative">
      <!-- BriefChat Section (Left) -->
      <div class="w-1/2 h-full overflow-y-auto flex-shrink">
        <BriefChat
          ref="briefChatRef"
          @update:brief="handleBriefUpdate"
          @update:raw-brief-stream="handleRawBriefStream"
          @brief-stream-complete="handleBriefStreamComplete"
          @auto-save-brief="handleAutoSaveBrief"
          @update:email="handleEmailUpdate"
          @update:raw-email-stream="handleRawEmailStream"
          @email-stream-complete="handleEmailStreamComplete"
          @auto-save-email="handleAutoSaveEmail"
          @generate-email-from-components="handleGenerateEmailFromComponentsEvent"
          :current-brief-text="editableBrief"
          :campaign-id="campaignId"
          :conversation-id="taskConversationId"
          :force-reload="forceReloadConversation"
        />
      </div>

      <!-- Campaign Brief/Email Section (Right) -->
      <div class="campaign-brief-container flex-1 bg-white shadow-xl border-l-2 border-t border-b border-gray-200 rounded-l-xl flex flex-col overflow-hidden ml-2 mt-4" style="min-width: 690px;">
        <!-- Artifact Top Bar with Tabs -->
        <div class="flex items-center justify-between p-2 border-b border-gray-200 flex-shrink-0 bg-gradient-to-l from-purple-50 to-white rounded-tl-xl">
		  <div class="flex items-center space-x-2">
				<div class="inline-flex p-1 bg-purple-100 bg-opacity-50 rounded-lg justify-center mx-auto">
				<button
				@click="activeTab = 'brief'"
				:class="[
					'px-4 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none',
					activeTab === 'brief' ? 'bg-white text-purple-700 shadow-sm' : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
				]"
				>
				Brief
				</button>
				<button
				@click="activeTab = 'email'"
				:class="[
					'px-4 py-1.5 text-xs font-medium rounded-md transition-all duration-200 focus:outline-none',
					activeTab === 'email' ? 'bg-white text-purple-700 shadow-sm' : 'text-gray-600 hover:text-purple-600 hover:bg-purple-50'
				]"
				>
				Email
				</button>
			</div>
		  </div>
		  <div class="flex items-center space-x-2">
			<!-- Right side elements like action buttons -->
		  </div>
        </div>

        <!-- Artifact Content Area -->
        <div class="flex-1 overflow-y-auto pl-6 pr-6 pt-2 space-y-6 mx-2 brief-scrollbar">
          <!-- Brief Tab Content -->
          <div v-if="activeTab === 'brief'">
			<!-- Subject and Preview Section -->
			<div class="mb-6">
				<SubjectPreviewEditor
				:subject-line="subjectLine"
				:preview-line="previewLine"
				:is-generating-subject="isGeneratingSubject"
				:is-generating-preview="isGeneratingPreview"
				:is-generating="isGenerating"
				@update:subject-line="$emit('update:subject-line', $event)"
				@update:preview-line="$emit('update:preview-line', $event)"
				/>
			</div>

          <!-- Brief Section -->
          <div v-if="briefContent">
            <div class="flex items-center justify-between mb-2">
              <span class="text-sm font-medium">Brief</span>
              <div class="flex items-center gap-2">
                <button
                  @click="downloadBrief"
                  class="text-xs text-gray-500 hover:text-gray-700 flex items-center justify-center gap-1 px-2 py-1 rounded border border-transparent hover:border-gray-300 transition-all duration-200 ease-in-out"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                  </svg>
                  Download
                </button>
                <button
                  @click="copyBrief"
                  class="text-xs text-gray-500 hover:text-gray-700 flex items-center justify-center gap-1 px-2 py-1 rounded border border-transparent hover:border-gray-300 transition-all duration-200 ease-in-out min-w-[60px]"
                  :class="{ 'bg-green-100 text-green-700': isCopied }"
                >
                  <span v-if="!isCopied" class="flex items-center gap-1">
                    <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="h-4 w-4">
                      <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                      <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g>
                      <g id="SVGRepo_iconCarrier">
                        <path d="M20.9983 10C20.9862 7.82497 20.8897 6.64706 20.1213 5.87868C19.2426 5 17.8284 5 15 5H12C9.17157 5 7.75736 5 6.87868 5.87868C6 6.75736 6 8.17157 6 11V16C6 18.8284 6 20.2426 6.87868 21.1213C7.75736 22 9.17157 22 12 22H15C17.8284 22 19.2426 22 20.1213 21.1213C21 20.2426 21 18.8284 21 16V15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                        <path d="M3 10V16C3 17.6569 4.34315 19 6 19M18 5C18 3.34315 16.6569 2 15 2H11C7.22876 2 5.34315 2 4.17157 3.17157C3.51839 3.82475 3.22937 4.69989 3.10149 6" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"></path>
                      </g>
                    </svg>
                    Copy
                  </span>
                  <span v-else>
                    Copied!
                  </span>
                </button>
                <!-- Save Button (Added here) -->
                <button
                  @click="saveBrief"
                  class="text-xs flex items-center justify-center gap-1 px-2 py-1 rounded border transition-all duration-200 ease-in-out min-w-[60px]"
                  :class="{
                    'bg-purple-600 text-white border-purple-600 hover:bg-purple-700 hover:border-purple-700': !isSaving,
                    'bg-purple-100 text-purple-700 border-purple-100': isSaving
                  }"
                  :disabled="isGenerating || isSaving"
                >
                  <span v-if="!isSaving" class="flex items-center gap-1">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
                    </svg>
                    Save
                  </span>
                  <span v-else class="flex items-center gap-1">
                    <svg class="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Saving...
                  </span>
                </button>
              </div>
            </div>

            <!-- Brief Content Container -->
            <div class="min-h-[10rem] flex-1">
              <!-- Streaming Mode: Show raw JSON -->
              <div v-if="isBriefStreaming" class="p-4 bg-gray-100 rounded-md flex flex-col">
                <div class="flex items-center mb-3">
                  <!-- Simplified Raleon Loader -->
                  <div class="raleon-loader-inline mr-3">
                    <svg width="24" height="18" viewBox="0 0 834 605" fill="none" xmlns="http://www.w3.org/2000/svg" class="rtop-inline">
                      <path fill-rule="evenodd" clip-rule="evenodd" d="M417 0L833.341 523.732L755.961 604.265L714.292 554.032L747.276 519.704L417 104.237L86.7254 519.704L119.709 554.032L78.0404 604.265L0.660156 523.732L417 0Z" fill="#5A16C9"/>
                    </svg>
                  </div>
                  <div>
                    <div class="text-gray-700 font-medium">Generating Brief</div>
                    <div class="text-sm text-gray-500">Receiving data...</div>
                  </div>
                </div>
                <pre class="whitespace-pre-wrap font-mono flex-1 overflow-y-auto bg-white p-3 rounded-md border border-gray-200">{{ streamingRawJson }}</pre>
              </div>

              <!-- TipTap Editor (Always Visible) -->
              <div v-else-if="isEditorReady" class="tiptap-editor-container w-full" style="min-height: 10rem;">
                <!-- Editor Menu Bar -->
                <div class="tiptap-menubar bg-gray-100 p-1 rounded-t border border-gray-300 flex flex-wrap gap-1">
                  <button @click="editor?.chain().focus().toggleBold().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('bold') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M6 4h8a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path><path d="M6 12h9a4 4 0 0 1 4 4 4 4 0 0 1-4 4H6z"></path></svg>
                  </button>
                  <button @click="editor?.chain().focus().toggleItalic().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('italic') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="19" y1="4" x2="10" y2="4"></line><line x1="14" y1="20" x2="5" y2="20"></line><line x1="15" y1="4" x2="9" y2="20"></line></svg>
                  </button>
                  <button @click="editor?.chain().focus().toggleStrike().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('strike') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line><path d="M16 6C16 6 14.5 4 12 4C9.5 4 8 6 8 8C8 10 10 12 16 12C16 12 16 14 12 16C8 18 7 16 7 16"></path></svg>
                  </button>
                  <span class="border-r border-gray-300 mx-1"></span>
                  <button @click="editor?.chain().focus().toggleHeading({ level: 1 }).run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('heading', { level: 1 }) }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    H1
                  </button>
                  <button @click="editor?.chain().focus().toggleHeading({ level: 2 }).run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('heading', { level: 2 }) }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    H2
                  </button>
                  <button @click="editor?.chain().focus().toggleHeading({ level: 3 }).run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('heading', { level: 3 }) }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    H3
                  </button>
                  <span class="border-r border-gray-300 mx-1"></span>
                  <button @click="editor?.chain().focus().toggleBulletList().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('bulletList') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="9" y1="6" x2="20" y2="6"></line><line x1="9" y1="12" x2="20" y2="12"></line><line x1="9" y1="18" x2="20" y2="18"></line><circle cx="5" cy="6" r="2"></circle><circle cx="5" cy="12" r="2"></circle><circle cx="5" cy="18" r="2"></circle></svg>
                  </button>
                  <button @click="editor?.chain().focus().toggleOrderedList().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('orderedList') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="10" y1="6" x2="21" y2="6"></line><line x1="10" y1="12" x2="21" y2="12"></line><line x1="10" y1="18" x2="21" y2="18"></line><path d="M4 6h1v4"></path><path d="M4 10h2"></path><path d="M6 18H4c0-1 2-2 2-3s-1-1.5-2-1"></path></svg>
                  </button>
                  <span class="border-r border-gray-300 mx-1"></span>
                  <button @click="editor?.chain().focus().toggleBlockquote().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('blockquote') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"></path><path d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"></path></svg>
                  </button>
                  <button @click="editor?.chain().focus().toggleCode().run()"
                    :class="{ 'is-active bg-purple-200': editor?.isActive?.('code') }"
                    class="editor-btn"
                    :disabled="isGenerating"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="16 18 22 12 16 6"></polyline><polyline points="8 6 2 12 8 18"></polyline></svg>
                  </button>
                  <button @click="editor?.chain().focus().setHorizontalRule().run()" class="editor-btn" :disabled="isGenerating">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="16" height="16" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><line x1="5" y1="12" x2="19" y2="12"></line></svg>
                  </button>
                </div>

                <!-- Editor Content -->
                <editor-content
                  :editor="editor"
                  class="w-full h-full text-sm bg-white block overflow-auto outline-none focus:outline-none focus:ring-0"
                  :class="{ 'opacity-70 cursor-not-allowed': isGenerating }"
                />

                <!-- Save Button removed from here and moved to the top buttons area -->
              </div>

              <!-- Placeholder when editor is not ready -->
              <div v-else class="flex items-center justify-center w-full bg-white" style="min-height: 10rem;">
                <div class="text-gray-400 text-sm italic">
                  Loading editor...
                </div>
              </div>
            </div>
          </div>
          <div v-else class="text-sm text-gray-500">
            No brief content available.
          </div>
          </div>

          <!-- Email Tab Content -->
          <div v-if="activeTab === 'email'" class="h-full">
            <!-- Email Streaming State -->
            <div v-if="isEmailStreaming" class="p-4 bg-gray-100 rounded-md flex flex-col h-full">
              <div class="flex items-center mb-3">
                <!-- Simplified Raleon Loader -->
                <div class="raleon-loader-inline mr-3">
                  <svg width="24" height="18" viewBox="0 0 834 605" fill="none" xmlns="http://www.w3.org/2000/svg" class="rtop-inline animate-pulse">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M417 0L833.341 523.732L755.961 604.265L714.292 554.032L747.276 519.704L417 104.237L86.7254 519.704L119.709 554.032L78.0404 604.265L0.660156 523.732L417 0Z" fill="#5A16C9"/>
                  </svg>
                </div>
                <div>
                  <div class="text-gray-700 font-medium">Generating Email Design</div>
                  <div class="text-sm text-gray-500">Receiving data...</div>
                </div>
              </div>
              <pre class="whitespace-pre-wrap font-mono flex-1 overflow-y-auto bg-white p-3 rounded-md border border-gray-200">{{ streamingEmailJson }}</pre>
            </div>

            <!-- Email Loading State (for non-streaming generation) -->
            <div v-else-if="isGeneratingEmail || isGeneratingEmailLocal" class="flex flex-col items-center justify-center h-full p-8 text-center">
              <div class="raleon-loader mb-4">
                <svg width="40" height="30" viewBox="0 0 834 605" fill="none" xmlns="http://www.w3.org/2000/svg" class="rtop animate-pulse">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M417 0L833.341 523.732L755.961 604.265L714.292 554.032L747.276 519.704L417 104.237L86.7254 519.704L119.709 554.032L78.0404 604.265L0.660156 523.732L417 0Z" fill="#5A16C9"/>
                </svg>
              </div>
              <p class="text-lg font-medium text-gray-700">Generating Email Design</p>
              <p class="text-sm text-gray-500 mt-2">This may take a moment...</p>
              <p class="text-xs text-purple-600 mt-4">
                Converting component list to final design...
              </p>
            </div>

            <!-- Email Streaming State (for streaming content) -->
            <div v-else-if="isEmailStreaming && !isGeneratingEmailLocal" class="flex flex-col items-center justify-center h-full p-8 text-center">
              <div class="raleon-loader mb-4">
                <svg width="40" height="30" viewBox="0 0 834 605" fill="none" xmlns="http://www.w3.org/2000/svg" class="rtop animate-pulse">
                  <path fill-rule="evenodd" clip-rule="evenodd" d="M417 0L833.341 523.732L755.961 604.265L714.292 554.032L747.276 519.704L417 104.237L86.7254 519.704L119.709 554.032L78.0404 604.265L0.660156 523.732L417 0Z" fill="#5A16C9"/>
                </svg>
              </div>
              <p class="text-lg font-medium text-gray-700">Receiving Email Content</p>
              <p class="text-sm text-gray-500 mt-2">Streaming in progress...</p>
              <button
                v-if="streamingEmailJson && streamingEmailJson.includes('components')"
                @click="handleGenerateEmailFromComponents"
                class="mt-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center text-sm font-medium"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Generate Email from Components
              </button>
            </div>

            <!-- Email Preview -->
            <div v-else-if="emailHtml" class="h-full rounded flex flex-col">
              <!-- Edit Email Button -->
              <div v-if="emailDesign" class="flex justify-end items-center p-2 bg-white">
                <div class="group relative cursor-help mr-3">
                  <div class="flex items-center text-amber-600 text-xs">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" class="w-4 h-4 mr-1">
                      <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd" />
                    </svg>
                    <span>Warning: AI can overwrite changes</span>
                  </div>
                  <!-- Hover tooltip with more detailed warning - positioned top-left instead of bottom -->
                  <div class="absolute left-0 top-0 mt-6 w-64 bg-white shadow-lg rounded-lg p-2 text-xs text-gray-700 invisible group-hover:visible border border-gray-200 z-10">
                    <p>Finish iterating on the email in chat before using the editor. Changes made directly in the editor may be lost when generating new emails in chat.</p>
                  </div>
                </div>
                <button
                  @click="$emit('show-email-modal')"
                  class="px-4 py-2 bg-blue-50 border border-blue-200 text-blue-700 rounded-lg hover:bg-blue-100 hover:border-blue-300 transition-colors duration-200 flex items-center font-medium text-sm"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4 mr-2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                    <circle cx="12" cy="12" r="3"></circle>
                  </svg>
                  Edit Email
                </button>
              </div>
              <!-- Email Preview Iframe -->
              <iframe :srcdoc="emailHtml" class="w-full h-full border-0"></iframe>
            </div>

            <!-- Hidden EmailEditor instance for HTML generation -->
            <div style="position: absolute; left: -9999px; top: -9999px; height: 1px; width: 1px; overflow: hidden;">
              <EmailEditor
                ref="emailEditorRef"
                :project-id="'267562'"
                :options="{
                  displayMode: 'email',
                  features: {
                    preview: false,
                    imageEditor: false,
                    undoRedo: false,
                    stockImages: false,
                  },
                  tools: {
                    image: { enabled: false },
                    button: { enabled: false },
                    divider: { enabled: false },
                    text: { enabled: false },
                    html: { enabled: false },
                    menu: { enabled: false },
                    social: { enabled: false },
                    timer: { enabled: false },
                    video: { enabled: false },
                    form: { enabled: false },
                    row: { enabled: true }
                  }
                }"
                style="height: 1px; width: 1px; border: none;"
                @load="editorLoaded"
              />
            </div>

            <!-- Empty State -->
            <div v-if="!emailDesign && !isEmailStreaming && !isGeneratingEmail && !isGeneratingEmailLocal && !emailHtml" class="flex flex-col items-center justify-center h-full p-8 text-center">
              <button
                v-if="hasEmailGeneration && streamingEmailJson && streamingEmailJson.includes('components')"
                @click="handleGenerateEmailFromComponents"
                class="mb-4 px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-200 flex items-center text-sm font-medium"
              >
                <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                </svg>
                Generate Email from Components
              </button>
              <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-300 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
              </svg>
              <p class="text-lg font-medium text-gray-700">No Email Design Yet</p>
              <p v-if="hasEmailGeneration" class="text-sm text-gray-500 mt-2">Ask the AI to create an email design for your campaign</p>
              <p v-else class="text-sm text-gray-500 mt-2">Email generation is currently in early access. To get early access, please reach out to <a href="mailto:<EMAIL>" class="text-purple-600 hover:underline">Raleon</a>.</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, watch, PropType, nextTick, onBeforeUnmount, onMounted } from '@vue/runtime-core';
import { marked } from 'marked'; // Import marked
import { jsonrepair } from 'jsonrepair'; // Import jsonrepair for fixing malformed JSON
import SubjectPreviewEditor from './SubjectPreviewEditor.ts.vue';
import BriefChat from './BriefChat.ts.vue';
import AskRaleonPopup from './AskRaleonPopup.vue';
import { EmailEditor } from 'vue-email-editor';
import * as Utils from '../../../client-old/utils/Utils';
import * as OrganizationSettings from '../../services/organization-settings.js';

// TipTap imports
import { EditorContent, useEditor } from '@tiptap/vue-3';
import StarterKit from '@tiptap/starter-kit';
import { Markdown } from 'tiptap-markdown';
import Highlight from '@tiptap/extension-highlight';

interface BriefData {
  subjectLine?: string;
  previewText?: string;
  briefText?: string;
  [key: string]: any; // Allow other properties
}

interface TaskStep {
  id: string;
  taskTypeId: string; // Note: Original code used 'name', assuming 'taskTypeId' might be more accurate or intended
  name?: string; // Keep name if used for lookup
  title: string;
  data: string; // This is expected to be a JSON string for CONTENT type
}

export default defineComponent({
  name: 'ContentTask',
  components: {
    SubjectPreviewEditor,
    BriefChat,
    AskRaleonPopup,
    EditorContent,
    EmailEditor,
  },
  props: {
      contentSections: {
        type: Array as PropType<TaskStep[]>,
        default: () => []
      },
      subjectLine: {
        type: String,
        default: ''
      },
      previewLine: {
        type: String,
        default: ''
      },
      isGeneratingSubject: Boolean,
      isGeneratingPreview: Boolean,
      isGenerating: Boolean,
  	isGeneratingEmail: Boolean,
      countdownTime: {
        type: Number,
        default: 120
      },
      showAnySecond: Boolean,
      currentGenerationMessages: {
        type: Array,
        default: () => []
      },
      currentMessageIndex: {
        type: Number,
        default: 0
      },
      emailDesign: {
        type: [Object, String],
        default: null
      },
      campaignId: {
        type: String,
        default: null
      },
      taskConversationId: {
        type: [String, Number],
        default: null
      }
    },
  emits: [
    'regenerate', // Keep this? Or remove if no longer used internally? Task says add, so keeping for now.
    'open-regenerate-modal',
    'update:subject-line',
    'update:preview-line',
    'update:content', // Emits { id: string, newData: string }
    'save-design', // Emits { design: object, html: string }
    'show-email-modal'
  ],
  setup(props, { emit }) {
    // State variables for UI
    const isCopied = ref(false); // For copy button state
    const isSaving = ref(false); // For save button state
    const briefTextarea = ref<HTMLTextAreaElement | null>(null);
    const activeTab = ref('brief'); // Default to brief tab

    // --- Ask Raleon Popup State ---
    const isAskPopupVisible = ref(false);
    const askPopupPosition = ref<{ top: number; left: number }>({ top: 0, left: 0 });
    const selectedTextContext = ref('');
    const briefChatRef = ref<InstanceType<typeof BriefChat> | null>(null);

    // --- Streaming state for brief JSON ---
    const isBriefStreaming = ref(false);
    const streamingRawJson = ref('');

    // --- Email state ---
    const emailHtml = ref('');
    const isEmailStreaming = ref(false);
    const streamingEmailJson = ref('');
    const emailEditorRef = ref<any>(null);
    const isEditorLoaded = ref(false);
    const isGeneratingEmailLocal = ref(false); // Local state for email generation
    const hasEmailGeneration = ref(false); // Flag for email generation access

    // Store the last exported HTML to prevent infinite loops
    const lastExportedHtml = ref<string | null>(null);

    // Function to export HTML from the editor
    const exportHtml = () => {
      if (!isEditorLoaded.value || !emailEditorRef.value) {
        return;
      }

      emailEditorRef.value.editor.exportHtml((data: any) => {
        const { html, design } = data;

        // Check if HTML has changed
        if (lastExportedHtml.value === html) {
          return;
        }

        // Update last exported HTML
        lastExportedHtml.value = html;

        emailHtml.value = html;

        // Switch to email tab when HTML is ready
        activeTab.value = 'email';

        // Save the HTML and design to the task
        if (html && design) {
          const designToSave = {
            design: design,
            html: html
          };
          emit('save-design', designToSave);
        }
      });
    };

    // Store the last loaded design to prevent infinite loops
    const lastLoadedDesign = ref<string | null>(null);

    // Function to load design into the editor
    const loadDesignIntoEditor = async (design: any) => {
      try {
        if (!isEditorLoaded.value || !emailEditorRef.value) {
          // Wait for editor to be ready
          setTimeout(() => loadDesignIntoEditor(design), 500);
          return;
        }

        let designObj = design;
        if (typeof design === 'string') {
          try {
            designObj = JSON.parse(design);
          } catch (e) {
            console.error('Failed to parse email design string:', e);
            return;
          }
        }

        // Convert to string for comparison
        const designStr = JSON.stringify(designObj);

        // Check if this is the same design we just loaded
        if (lastLoadedDesign.value === designStr) {
          return;
        }

        // Update the last loaded design
        lastLoadedDesign.value = designStr;

        emailEditorRef.value.editor.loadDesign(JSON.parse(JSON.stringify(designObj)));

        // Export HTML after loading the design
        exportHtml();
      } catch (error) {
        console.error('Error loading design into editor:', error);
      }
    };

    // Function to handle editor loaded event
    const editorLoaded = () => {
      isEditorLoaded.value = true;

      // If we already have a design, load it
      if (props.emailDesign) {
        loadDesignIntoEditor(props.emailDesign);
      }
    };

    // Watch for changes to emailDesign prop
    watch(() => props.emailDesign, (newDesign) => {
      if (newDesign) {
        loadDesignIntoEditor(newDesign);
      }
    }, { immediate: true });

    // Function to generate email from components
    const generateEmailFromComponents = async (componentJSONText: string) => {
      // Set loading state
      isGeneratingEmailLocal.value = true;
	  activeTab.value = 'email';
      try {
        const response = await fetch(`${Utils.URL_DOMAIN}/planner/generate-email-from-component-list`, {
          method: 'POST',
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          },
          body: componentJSONText
        });

        if (!response.ok) {
          throw new Error(`API error: ${response.status}`);
        }

        const responseJSON = await response.json();

        // Update the email design and load it into the editor
        loadDesignIntoEditor(responseJSON);

        // Emit auto-save event to trigger immediate saving
        handleAutoSaveEmail(responseJSON);

        return responseJSON;
      } catch (error) {
        console.error("Error calling email generation API:", error);
        emailHtml.value = `
          <div style="padding: 20px; color: #721c24; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px;">
            <h3>Error Generating Email</h3>
            <p>There was an error generating the email. Please try again.</p>
          </div>
        `;
        return null;
      } finally {
        // Reset loading state after a short delay to ensure UI updates properly
        setTimeout(() => {
          isGeneratingEmailLocal.value = false;
        }, 500);
      }
    };

    // Store the last updated email data to prevent infinite loops
    const lastUpdatedEmail = ref<string | null>(null);

    // Handle email update from BriefChat
    const handleEmailUpdate = (emailData: any) => {

      // Check if this is the same data we just updated
      const emailDataStr = JSON.stringify(emailData);
      if (lastUpdatedEmail.value === emailDataStr) {
        return;
      }

      // Update the last updated email data
      lastUpdatedEmail.value = emailDataStr;

      loadDesignIntoEditor(emailData);

      // Auto-save the email design
      if (emailData) {
        const designToSave = {
          design: emailData,
          html: emailHtml.value // Include current HTML if available
        };
        emit('save-design', designToSave);
      }
    };

    // Store the last auto-saved email data to prevent infinite loops
    const lastAutoSavedEmail = ref<string | null>(null);

    // Handle auto-save event for email from BriefChat
    const handleAutoSaveEmail = (emailData: any) => {

      // Check if this is the same data we just saved
      const emailDataStr = JSON.stringify(emailData);
      if (lastAutoSavedEmail.value === emailDataStr) {
        return;
      }

      // Update the last auto-saved email data
      lastAutoSavedEmail.value = emailDataStr;

      // Update the local preview
      loadDesignIntoEditor(emailData);

      // Emit save-design event to parent component to save the email design
      if (emailData) {
        // Create a design object with the design data
        const designToSave = {
          design: emailData,
          html: emailHtml.value // Include current HTML if available
        };

        // Emit the event to the parent component
        emit('save-design', designToSave);
      }
    };

    // Handle raw email stream data
    const handleRawEmailStream = (rawJson: string) => {
      isEmailStreaming.value = true;
      streamingEmailJson.value = rawJson;
      // Switch to email tab to show streaming content
      activeTab.value = 'email';

      // We no longer automatically call the API here
      // This is now handled by the handleGenerateEmailFromComponentsEvent method
      // which is triggered by the generate-email-from-components event
    };

    // Handler for the generate-email-from-components event
    const handleGenerateEmailFromComponentsEvent = (componentJSONText: string) => {
      // Set the email tab as active
      activeTab.value = 'email';
      // Call the API directly with the provided JSON
      generateEmailFromComponents(componentJSONText);
    };

    // Handler for the Generate Email from Components button
    const handleGenerateEmailFromComponents = () => {
      if (streamingEmailJson.value) {
        try {
          // Try to parse the JSON to see if it contains components
          let emailData: any = null;

          // First, check if it's wrapped in <email> tags
          const emailTagRegex = /<email>\s*([\s\S]*?)\s*<\/email>/i;
          const match = streamingEmailJson.value.match(emailTagRegex);

          if (match && match[1]) {
            // Extract the JSON content from between the tags
            const jsonContent = match[1].trim();
            emailData = JSON.parse(jsonContent);
          } else {
            // Try to parse the entire content as JSON
            const jsonStartIndex = streamingEmailJson.value.indexOf('{');
            const jsonEndIndex = streamingEmailJson.value.lastIndexOf('}');

            if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
              const jsonContent = streamingEmailJson.value.substring(jsonStartIndex, jsonEndIndex + 1);
              emailData = JSON.parse(jsonContent);
            }
          }

          // If we have valid JSON with components, generate the email
          if (emailData && emailData.components && Array.isArray(emailData.components)) {
            generateEmailFromComponents(JSON.stringify(emailData));
          } else {
            console.error('No valid component data found in the streaming JSON');
          }
        } catch (error) {
          console.error('Error parsing email JSON for component generation:', error);
        }
      }
    };

    // Store the last stream completed email data to prevent infinite loops
    const lastStreamCompletedEmail = ref<string | null>(null);

    // Handle email stream completion
    const handleEmailStreamComplete = () => {
		isEmailStreaming.value = false;

      // Try to parse the JSON from the streaming content
      try {
        if (streamingEmailJson.value) {
          // Extract JSON content from between <email> tags if present
          const emailTagRegex = /<email>\s*([\s\S]*?)\s*<\/email>/i;
          const match = streamingEmailJson.value.match(emailTagRegex);

          let emailData: any = null;

          if (match && match[1]) {
            const jsonContent = match[1].trim();
            emailData = JSON.parse(jsonContent);
          } else {
            // Try to parse the entire content as JSON
            const jsonStartIndex = streamingEmailJson.value.indexOf('{');
            const jsonEndIndex = streamingEmailJson.value.lastIndexOf('}');

            if (jsonStartIndex !== -1 && jsonEndIndex !== -1 && jsonEndIndex > jsonStartIndex) {
              const jsonContent = streamingEmailJson.value.substring(jsonStartIndex, jsonEndIndex + 1);
              emailData = JSON.parse(jsonContent);
            }
          }

          // We no longer automatically call the API here for component lists
          // This is now handled by the handleGenerateEmailFromComponentsEvent method
          // which is triggered by the generate-email-from-components event

          // If this is a complete email design (not components), load it directly
          if (emailData && typeof emailData === 'object') {
            // Check if this is the same data we just processed
            const emailDataStr = JSON.stringify(emailData);
            if (lastStreamCompletedEmail.value === emailDataStr) {
              return;
            }

            // Update the last stream completed email data
            lastStreamCompletedEmail.value = emailDataStr;

            if (!('components' in emailData) && emailData.body) {
              // This is already a complete email design, load it directly
              loadDesignIntoEditor(emailData);

              // Auto-save the email design
              const designToSave = {
                design: emailData,
                html: emailHtml.value // Include current HTML if available
              };
              emit('save-design', designToSave);
            } else if ('components' in emailData && Array.isArray(emailData.components)) {
              // This is a component list, save it as JSON
              // We'll save this JSON for later use with generate-email-from-components
              // but we won't automatically generate the email yet
              const designToSave = {
                design: emailData,
                html: null
              };
              emit('save-design', designToSave);
            }
          }
        }
      } catch (error) {
        console.error('Error parsing email JSON from stream:', error);
      } finally {
        // Reset streaming state
        isEmailStreaming.value = false;
        streamingEmailJson.value = '';
      }
    };

    // Flag to force conversation reload
    const forceReloadConversation = ref(false);

    // Watch for taskConversationId changes and reload conversation if needed
    watch(() => props.taskConversationId, (newVal, oldVal) => {

      // Only reload if we have a valid conversation ID and it's different from the previous one
      // or if we're switching back to the ContentTask component
      if (newVal && (newVal !== oldVal || activeTab.value === 'brief')) {

        // Force a reload by setting a special flag
        forceReloadConversation.value = true;

        // Reset the flag after a short delay
        setTimeout(() => {
          forceReloadConversation.value = false;
        }, 100);
      }
    }, { immediate: true });

    // Store the last content sections to prevent unnecessary updates
    const lastContentSections = ref<string | null>(null);

    // Log when contentSections changes
    watch(() => props.contentSections, (newVal) => {
      // Convert to string for comparison
      const contentSectionsStr = JSON.stringify(newVal);

      // Check if content sections have actually changed
      if (lastContentSections.value === contentSectionsStr) {
        // Skip logging if unchanged
        return;
      }

      // Update last content sections
      lastContentSections.value = contentSectionsStr;

      if (newVal.length > 0) {
      }
    }, { immediate: true, deep: true });

    const briefContent = computed<TaskStep | undefined>(() => {
      // Prioritize finding by a specific identifier if available, e.g., name or taskTypeId
      let section = props.contentSections.find(s => s.name === 'Content');
      if (!section && props.contentSections.length > 0) {
        section = props.contentSections[0]; // Fallback to the first section
      }
      return section;
    });

    const parsedBriefData = computed<BriefData | null>(() => {
      if (!briefContent.value?.data) return null;
      try {
        // Check if the data contains <brief> tags and extract the content
        let dataToProcess = briefContent.value.data;
        const briefTagRegex = /<brief>\s*([\s\S]*?)\s*<\/brief>/i;
        const match = dataToProcess.match(briefTagRegex);

        if (match && match[1]) {
          // Found content between <brief> tags, use that for parsing
          dataToProcess = match[1].trim();
        }

        try {
          // First attempt: direct parsing
          return JSON.parse(dataToProcess);
        } catch (parseError) {
          console.warn('Initial JSON parse failed, trying with jsonrepair:', parseError);

          // Second attempt: try with jsonrepair
          const repairedJson = jsonrepair(dataToProcess);
          return JSON.parse(repairedJson);
        }
      } catch (error) {
        console.error('Failed to parse brief data JSON even with repair:', error, briefContent.value.data);
        if (typeof briefContent.value.data === 'string') {
             return { briefText: briefContent.value.data };
        }
        return null;
      }
    });

    const editableBrief = ref('');

    // Track selection for highlighting
    const selectionRange = ref({ from: 0, to: 0 });

    // TipTap editor setup
    const editor = useEditor({
      content: '', // Start with empty content, will be updated in watch
      extensions: [
        StarterKit,
        Markdown.configure({
          transformPastedText: true,
          transformCopiedText: false
        }),
        Highlight.configure({
          multicolor: false,
          HTMLAttributes: {
            class: 'bg-purple-200 text-purple-900 rounded px-1',
          },
        }),
      ],
      editorProps: {
        attributes: {
          class: 'prose prose-sm max-w-none focus:outline-none min-h-[10rem]',
          spellcheck: 'false',
        },
      },
      onUpdate: ({ editor }) => {
        // Update editableBrief when editor content changes
        editableBrief.value = editor.storage.markdown.getMarkdown();
      },
      onSelectionUpdate: ({ editor }) => {
        // Store the selection range for highlighting
        const { from, to } = editor.state.selection;
        selectionRange.value = { from, to };
      },
    });

    // Initialize editableBrief when parsedBriefData changes
    watch(parsedBriefData, (newData) => {

      const newBriefText = Utils.cleanMarkdown(newData?.briefText || '');
      editableBrief.value = newBriefText;

      // Update editor content if it exists
      if (editor.value) {
        // Use the markdown extension to parse the markdown content
        editor.value.commands.setContent(newBriefText, false);
      }
    }, { immediate: true });

    // Watch for changes to the editor instance and update content when it's ready
    watch(editor, (newEditor) => {
      if (newEditor && editableBrief.value) {
        newEditor.commands.setContent(editableBrief.value, false);
      }
    });

    // Rendered Markdown for view mode
    const renderedBrief = computed(() => {
      // Configure marked options if needed (e.g., sanitize, breaks)
      marked.setOptions({ breaks: true, gfm: true });
      return marked(editableBrief.value || '');
    });

    // Helper to check if editor is ready
    const isEditorReady = computed(() => {
      return editor.value !== null && editor.value !== undefined;
    });

    // --- Saving Logic ---
    const saveBriefContent = () => {
      if (!briefContent.value) {
        console.warn('Cannot save brief: Missing brief content section.');
        return;
      }

      const originalBriefText = parsedBriefData.value?.briefText || '';
      const originalSubjectLine = parsedBriefData.value?.subjectLine || '';
      const originalPreviewText = parsedBriefData.value?.previewText || '';

      // Check if the brief text actually changed before saving
      if (editableBrief.value === originalBriefText) {
        return;
      }

      try {
        // Create a new object, preserving other data from the original JSON string
        const baseData = parsedBriefData.value ? { ...parsedBriefData.value } : {};

        // Clean up the brief text
        let cleanBriefText = editableBrief.value;
        if (cleanBriefText.includes('<brief>') || cleanBriefText.includes('</brief>')) {
          console.warn('Found <brief> tags in briefText, removing them');
          cleanBriefText = cleanBriefText.replace(/<brief>|<\/brief>/g, '');
        }

        // Make sure we preserve the subject line and preview text
        const updatedData: BriefData = {
            ...baseData,
            subjectLine: originalSubjectLine, // Preserve the original subject line
            previewText: originalPreviewText, // Preserve the original preview text
            briefText: cleanBriefText // Update with the cleaned brief text
        };

        const newJsonString = JSON.stringify(updatedData, null, 2);

        // Double-check we're not saving with <brief> tags
        let dataToSave = newJsonString;
        if (dataToSave.includes('<brief>') || dataToSave.includes('</brief>')) {
          console.warn('Found <brief> tags in data to save, removing them');
          dataToSave = dataToSave.replace(/<brief>|<\/brief>/g, '');
        }

        emit('update:content', { id: briefContent.value.id, newData: dataToSave });

        // Force an update of the briefContent.value.data property to ensure the UI updates
        // This is needed because the parsedBriefData computed property depends on briefContent.value.data
        if (briefContent.value) {
          briefContent.value.data = dataToSave;
        }
      } catch (error) {
        console.error('Failed to stringify updated brief data:', error);
      }
    };

    // Focus the editor when needed
    const focusEditor = () => {
      nextTick(() => {
        if (editor.value) {
          editor.value.commands.focus();
        }
      });
    };

    // --- Autosize Logic ---
    const resizeTextarea = () => {
      if (briefTextarea.value) {
        briefTextarea.value.style.height = 'auto'; // Reset height to recalculate
        // Use a small timeout or nextTick again if scrollHeight isn't updated immediately
        nextTick(() => {
          if (briefTextarea.value) { // Check again inside nextTick
             briefTextarea.value.style.height = `${briefTextarea.value.scrollHeight}px`;
          }
        });
      }
    };

    // Save the brief content with animation
    const saveBrief = async () => {
      // Show saving animation
      isSaving.value = true;

      // Get the latest content from the editor
      if (editor.value) {
        editableBrief.value = editor.value.storage.markdown.getMarkdown();
      }

      try {
        // Save the current content using the original saveBrief function
        saveBriefContent();

        // Keep the animation visible for at least 700ms for better UX
        await new Promise(resolve => setTimeout(resolve, 700));

        // Log the saved content for debugging
      } finally {
        // Hide saving animation
        isSaving.value = false;
      }
    };

    // --- Copy Logic ---
    const copyBrief = () => {
      const textToCopy = editableBrief.value || '';
      if (!textToCopy) {
        console.warn('No brief text available to copy.');
        return;
      }
      navigator.clipboard.writeText(textToCopy)
        .then(() => {
          isCopied.value = true; // Set copied state to true
          setTimeout(() => {
            isCopied.value = false; // Reset after 2 seconds
          }, 2000);
        })
        .catch(err => {
          console.error('Failed to copy brief text: ', err);
          // Optional: Show an error message
        });
    };

    // --- Download Logic ---
    const downloadBrief = () => {
      const textToDownload = Utils.markdownToPlainText(editableBrief.value || '');
      if (!textToDownload) {
        console.warn('No brief text available to download.');
        return;
      }
      const blob = new Blob([textToDownload], { type: 'text/plain;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'campaign-brief.txt');
      document.body.appendChild(link); // Required for Firefox
      link.click();
      document.body.removeChild(link); // Clean up
      URL.revokeObjectURL(url); // Free up memory
    };

    // --- Utility (if needed) ---
    const formatCountdown = (seconds: number) => {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = seconds % 60;
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    };

    // --- Event Handlers ---
    const handleRawBriefStream = (rawJson: string) => {
      isBriefStreaming.value = true;
	  activeTab.value = 'brief';

      // Store the raw JSON for display, but clean it up if needed
      let displayJson = rawJson;

      // If we're getting raw JSON with <brief> tags, we should display it properly
      // but keep the tags in the raw stream for proper processing later
      if (displayJson.includes('<brief>') && !displayJson.includes('</brief>')) {
        // If we have an opening tag but no closing tag, it's still streaming
        // Just display the content without the opening tag for better readability
        displayJson = displayJson.replace('<brief>', '');
      }

      streamingRawJson.value = displayJson;
    };

    const handleBriefStreamComplete = () => {
      isBriefStreaming.value = false;
      streamingRawJson.value = '';
    };

    const handleBriefUpdate = (payload: { subjectLine: string, previewText: string, briefText: string }) => {

      // Ensure streaming state is off (parsing is done)
      isBriefStreaming.value = false;
      streamingRawJson.value = '';

      // Clean up the payload data
      let cleanSubjectLine = payload.subjectLine || '';
      let cleanPreviewText = payload.previewText || '';
      let cleanBriefText = payload.briefText || '';

      // Check if any fields contain <brief> tags and remove them
      if (cleanSubjectLine.includes('<brief>') || cleanSubjectLine.includes('</brief>')) {
        console.warn('Found <brief> tags in subjectLine, removing them');
        cleanSubjectLine = cleanSubjectLine.replace(/<brief>|<\/brief>/g, '');
      }

      if (cleanPreviewText.includes('<brief>') || cleanPreviewText.includes('</brief>')) {
        console.warn('Found <brief> tags in previewText, removing them');
        cleanPreviewText = cleanPreviewText.replace(/<brief>|<\/brief>/g, '');
      }

      if (cleanBriefText.includes('<brief>') || cleanBriefText.includes('</brief>')) {
        console.warn('Found <brief> tags in briefText, removing them');
        cleanBriefText = cleanBriefText.replace(/<brief>|<\/brief>/g, '');
      }

      // Log the cleaned values for debugging

      // Emit updates for subject and preview lines with cleaned data
      emit('update:subject-line', cleanSubjectLine);
      emit('update:preview-line', cleanPreviewText);

      // Update the main brief content
      if (!briefContent.value) {
        console.warn('Cannot update brief: Missing brief content section.');
        return;
      }

      // Also update the editor with the cleaned brief text
      if (editor.value && cleanBriefText) {
        editableBrief.value = cleanBriefText;
        editor.value.commands.setContent(cleanBriefText, false);
      }

      // Save the cleaned brief data
      try {
        // Get the current parsed data (or an empty object if none exists)
        const currentData = parsedBriefData.value || {};

        // Create the new data object with cleaned values
        const updatedData: BriefData = {
          ...currentData,
          subjectLine: cleanSubjectLine,
          previewText: cleanPreviewText,
          briefText: cleanBriefText
        };

        // Stringify and save the updated data
        const newJsonString = JSON.stringify(updatedData, null, 2);

        // Update the briefContent data
        if (briefContent.value) {
          briefContent.value.data = newJsonString;

          // Force a re-evaluation of the parsedBriefData computed property
          // This ensures the UI updates with the new values
          nextTick(() => {
          });
        }
      } catch (error) {
        console.error('Failed to update brief content with cleaned values:', error);
      }
    };

    // Handle auto-save event from BriefChat
    const handleAutoSaveBrief = (payload: { subjectLine: string, previewText: string, briefText: string }) => {

      // Clean up the payload data
      let cleanSubjectLine = payload.subjectLine || '';
      let cleanPreviewText = payload.previewText || '';
      let cleanBriefText = payload.briefText || '';

      // Check if any fields contain <brief> tags and remove them
      if (cleanSubjectLine.includes('<brief>') || cleanSubjectLine.includes('</brief>')) {
        console.warn('Found <brief> tags in subjectLine, removing them');
        cleanSubjectLine = cleanSubjectLine.replace(/<brief>|<\/brief>/g, '');
      }

      if (cleanPreviewText.includes('<brief>') || cleanPreviewText.includes('</brief>')) {
        console.warn('Found <brief> tags in previewText, removing them');
        cleanPreviewText = cleanPreviewText.replace(/<brief>|<\/brief>/g, '');
      }

      if (cleanBriefText.includes('<brief>') || cleanBriefText.includes('</brief>')) {
        console.warn('Found <brief> tags in briefText, removing them');
        cleanBriefText = cleanBriefText.replace(/<brief>|<\/brief>/g, '');
      }

      // Log the cleaned values for debugging

      // Emit updates for subject and preview lines with cleaned data
      emit('update:subject-line', cleanSubjectLine);
      emit('update:preview-line', cleanPreviewText);

      // Update and save the brief content
      if (!briefContent.value) {
        console.warn('Cannot auto-save brief: Missing brief content section.');
        return;
      }

      try {
        // Get the current parsed data (or an empty object if none exists)
        const currentData = parsedBriefData.value || {};

        // Create the new data object, merging existing data with the new brief text
        const updatedData: BriefData = {
          ...currentData,
          subjectLine: cleanSubjectLine,
          previewText: cleanPreviewText,
          briefText: cleanBriefText
        };

        // Stringify the updated data - ensure we're not wrapping in <brief> tags
        const newJsonString = JSON.stringify(updatedData, null, 2); // Pretty print JSON

        // Double-check we're not saving with <brief> tags
        let dataToSave = newJsonString;
        if (dataToSave.includes('<brief>') || dataToSave.includes('</brief>')) {
          console.warn('Found <brief> tags in data to save, removing them');
          dataToSave = dataToSave.replace(/<brief>|<\/brief>/g, '');
        }

        // Emit the update:content event for the parent to handle saving
        emit('update:content', { id: briefContent.value.id, newData: dataToSave });

        // Update the local editableBrief ref immediately for responsiveness
        editableBrief.value = cleanBriefText;

        // Update the editor content if it exists
        if (editor.value) {
          editor.value.commands.setContent(cleanBriefText, false);
        }

        // Force an update of the briefContent.value.data property to ensure the UI updates
        // This is needed because the parsedBriefData computed property depends on briefContent.value.data
        if (briefContent.value) {
          briefContent.value.data = dataToSave;

          // Force a re-evaluation of the parsedBriefData computed property
          // This ensures the UI updates with the new values
          nextTick(() => {
          });
        }
      } catch (error) {
        console.error('Failed to auto-save brief:', error);
      }
    };


    // Create a named function for the beforeunload event handler
    const handleBeforeUnload = () => {
      if (editor.value && editableBrief.value) {
        saveBriefContent(); // Use saveBriefContent directly to avoid async issues
      }
    };

    // Initialize component when mounted
    onMounted(async () => {
      // If we already have brief content but the editor hasn't been updated yet
      if (editableBrief.value && editor.value) {
        editor.value.commands.setContent(editableBrief.value, false);
      }

      // Add event listener to save content when navigating away
      window.addEventListener('beforeunload', handleBeforeUnload);

      // Check if organization has email generation access
      try {
        const emailGenerationSetting = await OrganizationSettings.getOrganizationSetting('hasEmailGeneration');
        hasEmailGeneration.value = emailGenerationSetting === 'true';
      } catch (error) {
        console.error('Error checking email generation access:', error);
        hasEmailGeneration.value = false;
      }
    });

    // Clean up editor and event listeners on component unmount
    onBeforeUnmount(() => {
      // Save content before destroying editor
      if (editor.value && editableBrief.value) {
        saveBriefContent();
      }

      // Remove event listener with the same function reference
      window.removeEventListener('beforeunload', handleBeforeUnload);

      // Remove click listener for AskRaleonPopup if present
      document.removeEventListener('mousedown', handleAskPopupOutsideClick);

      // Destroy editor
      editor.value?.destroy();
    });

    // --- Ask Raleon Selection Logic ---
    let askPopupDebounceTimeout: number | null = null;
    let popupReopenDebounce: number | null = null;
    let canShowPopup = true;

    function handleAskPopupOutsideClick(e: MouseEvent) {
      // If popup is not visible, do nothing
      if (!isAskPopupVisible.value) return;
      // If click is inside the popup, do nothing
      const popupEl = document.querySelector('.ask-raleon-popup-root');
      if (popupEl && popupEl.contains(e.target as Node)) return;
      isAskPopupVisible.value = false;
    }

    // Function to handle scroll events
    function handleScroll(e: Event) {
      // Check if the event is coming from the popup textarea
      const target = e.target as HTMLElement;
      const popupTextarea = document.querySelector('.ask-raleon-popup-root textarea');
      
      // If the scroll is from the popup textarea, don't close it
      if (popupTextarea && (target === popupTextarea || target.contains(popupTextarea))) {
        return;
      }
      
      if (isAskPopupVisible.value) {
        isAskPopupVisible.value = false;
      }
    }

    // Function to prevent popup from reopening too quickly
    function preventPopupReopen() {
      canShowPopup = false;
      if (popupReopenDebounce) {
        clearTimeout(popupReopenDebounce);
      }
      popupReopenDebounce = window.setTimeout(() => {
        canShowPopup = true;
      }, 300); // Wait 300ms before allowing popup to show again
    }

    watch(
      () => editor.value?.state.selection,
      () => {
        if (!editor.value) return;
        // Don't show popup if we're in the debounce period
        if (!canShowPopup) return;

        // Debounce to avoid flicker
        if (askPopupDebounceTimeout) {
          clearTimeout(askPopupDebounceTimeout);
        }
        askPopupDebounceTimeout = window.setTimeout(() => {
          const sel = editor.value!.state.selection;
          if (!sel || sel.empty) {
            isAskPopupVisible.value = false;
            selectedTextContext.value = '';
            return;
          }
          // Only show if selection is not empty and is in the brief tab
          if (activeTab.value !== 'brief') {
            isAskPopupVisible.value = false;
            selectedTextContext.value = '';
            return;
          }
          // Get selected text
          const from = sel.from;
          const to = sel.to;
          const text = editor.value!.state.doc.textBetween(from, to, ' ');
          if (!text || text.trim() === '') {
            isAskPopupVisible.value = false;
            selectedTextContext.value = '';
            return;
          }
          selectedTextContext.value = text;

          // Don't apply highlight immediately - will be applied on input focus

          // Get position for popup (bottom of selection)
          const view = editor.value!.view;
          const fromCoords = view.coordsAtPos(from);
          const toCoords = view.coordsAtPos(to);

          // Get the editor element's position
          const editorElement = document.querySelector('.ProseMirror');
          const editorRect = editorElement?.getBoundingClientRect();
          const containerRect = document.querySelector('.h-full.relative')?.getBoundingClientRect();

          if (editorRect && containerRect && fromCoords && toCoords) {
            // Calculate position relative to the container
            const relativeTop = toCoords.bottom - containerRect.top;

            // Calculate the center point of the selection
            const selectionCenter = (fromCoords.left + toCoords.left) / 2;
            const relativeLeft = selectionCenter - containerRect.left;

            // Position popup directly below the selection center
            askPopupPosition.value = {
              top: relativeTop + 10,
              left: relativeLeft,
            };
          } else {
            // Fallback if elements not found
            askPopupPosition.value = {
              top: (toCoords?.bottom ?? 0) + 10,
              left: (fromCoords?.left + (toCoords?.left ?? 0)) / 2,
            };
          }
          isAskPopupVisible.value = true;
        }, 80);
      }
    );

    // Watch popup visibility to add/remove event listeners and handle highlighting
    watch(isAskPopupVisible, (visible) => {
      if (visible) {
        document.addEventListener('mousedown', handleAskPopupOutsideClick);
        // Add scroll event listeners to hide popup on scroll
        document.addEventListener('scroll', handleScroll, true); // Use capture phase
        const editorElement = document.querySelector('.ProseMirror');
        if (editorElement) {
          editorElement.addEventListener('scroll', handleScroll);
        }
        const briefScrollbar = document.querySelector('.brief-scrollbar');
        if (briefScrollbar) {
          briefScrollbar.addEventListener('scroll', handleScroll);
        }
      } else {
        document.removeEventListener('mousedown', handleAskPopupOutsideClick);
        document.removeEventListener('scroll', handleScroll, true);
        const editorElement = document.querySelector('.ProseMirror');
        if (editorElement) {
          editorElement.removeEventListener('scroll', handleScroll);
        }
        const briefScrollbar = document.querySelector('.brief-scrollbar');
        if (briefScrollbar) {
          briefScrollbar.removeEventListener('scroll', handleScroll);
        }
        // Highlight will be removed on input blur or submit
      }
    });

    // Clean up event listeners on component unmount
    onBeforeUnmount(() => {
      document.removeEventListener('mousedown', handleAskPopupOutsideClick);
      document.removeEventListener('scroll', handleScroll, true);
      const editorElement = document.querySelector('.ProseMirror');
      if (editorElement) {
        editorElement.removeEventListener('scroll', handleScroll);
      }
      const briefScrollbar = document.querySelector('.brief-scrollbar');
      if (briefScrollbar) {
        briefScrollbar.removeEventListener('scroll', handleScroll);
      }
    });

    // --- Highlight Handlers ---
    function handleSetHighlight() {
      if (editor.value) {
        editor.value.chain().setHighlight().run();
      }
    }

    function handleRemoveHighlight() {
      if (editor.value) {
        editor.value.chain().unsetHighlight().run();
      }
    }
    
    // Prevent popup from closing during typing
    function handlePreventPopupClose() {
      // Reset any pending debounce timeouts that might close the popup
      if (askPopupDebounceTimeout) {
        clearTimeout(askPopupDebounceTimeout);
      }
      
      // Ensure popup remains visible
      if (!isAskPopupVisible.value) {
        return;
      }
      
      // Create a temporary protection period where popup cannot be closed by selection changes
      canShowPopup = true;
      
      // Clear any pending timeouts for preventing reopening
      if (popupReopenDebounce) {
        clearTimeout(popupReopenDebounce);
      }
    }

    // --- AskRaleon Query Submission Handler ---
    function handleAskRaleonQuerySubmit(userQuery: string) {
      if (!briefChatRef.value) return;
      const context = selectedTextContext.value;
      const formattedMessage = `Change this text:
> ${context} from the brief.

${userQuery}`;
      // Call sendMessage on BriefChat
      // @ts-ignore
      briefChatRef.value.sendMessage(formattedMessage);
      isAskPopupVisible.value = false;

      // Prevent popup from reopening too quickly
      preventPopupReopen();

      // Clear the selection to prevent the popup from reopening
      if (editor.value) {
        // Use setTimeout to ensure this happens after the current event cycle
        setTimeout(() => {
          if (editor.value) {
            // Create an empty selection at the current cursor position
            const { from } = editor.value.state.selection;
            editor.value.commands.setTextSelection(from);
          }
        }, 10);
      }
    }

    return {
      briefTextarea,
      editableBrief,
      renderedBrief, // Use this for v-html
      focusEditor,
      saveBrief,
      resizeTextarea, // Expose for template use
      copyBrief,
      isCopied, // Expose for template use
      isSaving, // Expose for template use
      downloadBrief, // Expose for template use
      formatCountdown, // Keep if used in template
      briefContent, // Keep for v-if check
      handleBriefUpdate, // Expose the new handler
      handleAutoSaveBrief, // Expose the auto-save handler
      handleRawBriefStream,
      handleBriefStreamComplete,
      isBriefStreaming,
      streamingRawJson,
      editor, // Expose the TipTap editor
      isEditorReady, // Helper for checking if editor is ready
      // Email-related properties and methods
      activeTab,
      emailHtml,
      isEmailStreaming,
      isGeneratingEmailLocal,
      streamingEmailJson,
      forceReloadConversation,
      handleEmailUpdate,
      handleAutoSaveEmail,
      handleRawEmailStream,
      handleEmailStreamComplete,
      loadDesignIntoEditor,
      generateEmailFromComponents,
      handleGenerateEmailFromComponents,
      handleGenerateEmailFromComponentsEvent,
      exportHtml,
      editorLoaded,
      emailEditorRef,
      // AskRaleonPopup integration
      isAskPopupVisible,
      askPopupPosition,
      selectedTextContext,
      briefChatRef,
      handleAskRaleonQuerySubmit,
      handleSetHighlight,
      handleRemoveHighlight,
      handlePreventPopupClose,
      // Expose other necessary props/methods
      // Use computed refs for props passed down to ensure reactivity
      subjectLine: computed(() => props.subjectLine),
      previewLine: computed(() => props.previewLine),
      isGenerating: computed(() => props.isGenerating),
      isGeneratingSubject: computed(() => props.isGeneratingSubject),
      isGeneratingPreview: computed(() => props.isGeneratingPreview),
      isGeneratingEmail: computed(() => props.isGeneratingEmail || isGeneratingEmailLocal.value),
      emailDesign: computed(() => props.emailDesign),
      hasEmailGeneration,
    };
  }
})
</script>

<style scoped lang="css">
/* TipTap Editor Styles */
.tiptap-editor-container {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.tiptap-menubar {
  display: flex;
  flex-wrap: wrap;
}

.editor-btn {
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background: white;
  border: 1px solid #e5e7eb;
  color: #4b5563;
  font-size: 0.875rem;
  line-height: 1;
}

.editor-btn:hover {
  background-color: #f3f4f6;
}

.editor-btn.is-active {
  background-color: #e9d5ff;
  color: #6b21a8;
}

:deep(.ProseMirror) {
  min-height: 10rem;
  outline: none;
  background-color: white;
  width: 100%;
}

:deep(.ProseMirror p.is-editor-empty:first-child::before) {
  content: attr(data-placeholder);
  float: left;
  color: #adb5bd;
  pointer-events: none;
  height: 0;
}

:deep(.ProseMirror) p {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}

:deep(.ProseMirror) h1,
:deep(.ProseMirror) h2,
:deep(.ProseMirror) h3,
:deep(.ProseMirror) h4,
:deep(.ProseMirror) h5,
:deep(.ProseMirror) h6 {
  margin-top: 1rem;
  margin-bottom: 0.5rem;
  font-weight: 600;
}

:deep(.ProseMirror) h1 {
  font-size: 1.5rem;
}

:deep(.ProseMirror) h2 {
  font-size: 1.25rem;
}

:deep(.ProseMirror) h3 {
  font-size: 1.125rem;
}

:deep(.ProseMirror) ul,
:deep(.ProseMirror) ol {
  padding-left: 1.5rem;
}

:deep(.ProseMirror) blockquote {
  border-left: 3px solid #e5e7eb;
  padding-left: 1rem;
  margin-left: 0;
  margin-right: 0;
  font-style: italic;
}

:deep(.ProseMirror) code {
  background-color: #f1f1f1;
  padding: 0.2rem 0.4rem;
  border-radius: 0.25rem;
  font-family: monospace;
}

:deep(.ProseMirror) pre {
  background-color: #f1f1f1;
  padding: 0.75rem 1rem;
  border-radius: 0.25rem;
  overflow-x: auto;
}

:deep(.ProseMirror) pre code {
  background-color: transparent;
  padding: 0;
  border-radius: 0;
}

:deep(.ProseMirror) hr {
  border: none;
  border-top: 2px solid #e5e7eb;
  margin: 1rem 0;
}

/* Highlight styles */
:deep(.ProseMirror) mark {
  background-color: #ddd6fe; /* purple-200 */
  color: #581c87; /* purple-900 */
  border-radius: 0.25rem;
  padding: 0 0.25rem;
  transition: background-color 0.2s ease;
}
/* Basic styling for the view/edit container */
.border {
  border-color: #e5e7eb; /* Example: gray-200 */
}
.rounded {
  border-radius: 0.375rem; /* Example: rounded-md */
}
.p-3 {
  padding: 0.75rem;
}
.min-h-\[10rem\] {
  min-height: 10rem;
}

/* Ensure textarea takes full height in edit mode */
textarea {
    display: block; /* Ensure it behaves like a block element */
    box-sizing: border-box; /* Include padding and border in element's total width and height */
}

/* Button animations (kept from original) */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.no-focus-outline {
	box-shadow: none !important;
	outline: none !important;
}

/* Add animation for the artifact drawer */
@keyframes slideInFromRight {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Apply the animation to the Campaign Brief component */
.rounded-l-xl {
  animation: slideInFromRight 0.3s ease-out forwards;
}

.brief-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: #b1b2b4 #FFFFFF;
}

.brief-scrollbar::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

.brief-scrollbar::-webkit-scrollbar-track {
  background: #FFFFFF;
  border-radius: 9999px;
}

.brief-scrollbar::-webkit-scrollbar-thumb {
  background-color: #8e9299;
  border-radius: 9999px;
  border: 2px solid transparent;
  background-clip: content-box;
}

.brief-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #8e9299;
}

/* Custom height for campaign brief container */
.campaign-brief-container {
  height: calc(100% - 50px);
}
</style>

