<template>
  <div class="mt-4">
    <label class="block text-sm font-medium text-gray-700 mb-1">Editable AI Fields</label>
    <div class="border border-gray-300 rounded-md p-3 h-64 overflow-y-auto">
      <p class="text-sm text-gray-500 mb-2">Select fields that can be edited by AI and provide descriptions:</p>

      <!-- Loop through component groups -->
      <div v-for="(paths, componentId) in groupedFieldPaths" :key="componentId" class="mb-4">
        <div class="font-medium text-gray-800 mb-1">
          {{ getComponentName(componentId) }}
          <span class="text-xs text-gray-500">({{ getComponentType(componentId) }})</span>
        </div>

        <!-- Field checkboxes with descriptions -->
        <div v-for="path in paths" :key="path" class="ml-2 mb-2">
          <div class="flex items-start">
            <div class="flex items-center h-5">
              <input :id="`field-${path}`"
                type="checkbox"
                v-model="editableFields[path].editable"
                class="focus:ring-indigo-500 h-4 w-4 text-indigo-600 border-gray-300 rounded">
            </div>
            <div class="ml-2 w-full">
              <label :for="`field-${path}`" class="text-sm font-medium text-gray-700">
                {{ getFieldLabel(path) }}
              </label>
              <div class="mt-1">
                <input type="text"
                      v-model="editableFields[path].description"
                      :disabled="!editableFields[path].editable"
                      placeholder="Field description (optional)"
                      class="shadow-sm focus:ring-indigo-500 focus:border-indigo-500 block w-full sm:text-sm border-gray-300 rounded-md">
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Message when no fields are detected -->
      <div v-if="Object.keys(groupedFieldPaths).length === 0" class="text-sm text-gray-500">
        No editable fields detected. Make sure your JSON contains valid Unlayer components.
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EditableFieldsSection',
  props: {
    editableFields: {
      type: Object,
      required: true
    },
    groupedFieldPaths: {
      type: Object,
      required: true
    },
    componentTypesMap: {
      type: Object,
      required: true
    },
    componentNamesMap: {
      type: Object,
      required: true
    }
  },
  methods: {
    getComponentName(componentId) {
      return this.componentNamesMap[componentId] || componentId;
    },
    getComponentType(componentId) {
      return this.componentTypesMap[componentId] || 'unknown';
    },
    getFieldLabel(path) {
      let componentType = 'unknown';
      for (const [contentId, type] of Object.entries(this.componentTypesMap)) {
        if (path.includes(contentId)) {
          componentType = type;
          break;
        }
      }

      if (path.endsWith('.values.text')) {
        if (componentType === 'heading') return 'Heading Text';
        else if (componentType === 'text') return 'Text Content';
        else if (componentType === 'button') return 'Button Text';
        else return 'Text';
      } else if (path.endsWith('.values.src.url')) {
        return 'Image URL';
      } else if (path.endsWith('.values.altText')) {
        return 'Alt Text';
      } else if (path.endsWith('.values.href.values.href')) {
        return 'Button URL';
      } else {
        const lastPart = path.split('.').pop() || '';
        return lastPart.charAt(0).toUpperCase() + lastPart.slice(1);
      }
    }
  }
};
</script>
